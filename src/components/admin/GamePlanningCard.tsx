import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/custom-card";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Calendar as CalendarIcon,
  Clock,
  Users,
  CheckCircle,
  Settings,
  CalendarDays,
  Lock,
  Unlock,
  Timer,
  X,
  AlertCircle,
  ChevronRight,
  Play,
  Pause,
  RotateCcw,
  Zap,
  Activity,
  Target,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { GameSession } from "@/types";
import LoadingSpinner from "@/components/LoadingSpinner";
import { formatDate } from "@/utils/dateUtils";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface GamePlanningCardProps {
  currentSession: GameSession | null;
  gameDate: Date;
  gameTime: string;
  gameFormation: string;
  canGenerateTeams: boolean;
  showMarkAsPlayedButton: boolean;
  isGeneratingTeams: boolean;
  isMarkingAsPlayed: boolean;
  isCancelingMatch: boolean;
  isPendingTeamApproval?: boolean;
  isSignupOpen: boolean;
  onDateChange: (date: Date | undefined) => void;
  onTimeChange: (time: string) => void;
  onSave: () => void;
  onToggleSignup: () => void;
  onGenerateTeams: () => void;
  onMarkAsPlayed: () => void;
  onCancelMatch: () => void;
}

// Game Status Header Component
function GameStatusHeader({
  currentSession,
  gameFormation,
  isSignupOpen
}: {
  currentSession: GameSession | null;
  gameFormation: string;
  isSignupOpen: boolean;
}) {
  if (!currentSession) {
    return (
      <div className="text-center py-8">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-zinc-100 dark:bg-zinc-800 mb-4">
          <CalendarIcon className="w-8 h-8 text-zinc-400" />
        </div>
        <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-2">
          Kein aktives Spiel
        </h3>
        <p className="text-sm text-zinc-500 dark:text-zinc-400">
          Erstellen Sie ein neues Spiel, um zu beginnen
        </p>
      </div>
    );
  }

  const getStatusColor = () => {
    if (currentSession.status === "scheduled") {
      return isSignupOpen ? "bg-green-500" : "bg-amber-500";
    }
    return "bg-zinc-500";
  };

  const getStatusText = () => {
    if (currentSession.status === "scheduled") {
      return isSignupOpen ? "Anmeldung offen" : "Anmeldung geschlossen";
    }
    return "Inaktiv";
  };

  return (
    <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-team-primary/10 via-team-primary/5 to-transparent dark:from-team-primary/20 dark:via-team-primary/10 p-6 border border-team-primary/20">
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={cn("w-3 h-3 rounded-full", getStatusColor())} />
            <Badge variant="outline" className="text-xs font-medium">
              {getStatusText()}
            </Badge>
          </div>
          <div className="text-right">
            <p className="text-sm text-zinc-500 dark:text-zinc-400">Nächstes Spiel</p>
            <p className="font-semibold text-zinc-900 dark:text-zinc-100">
              {formatDate(currentSession.date)}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-xs text-zinc-500 dark:text-zinc-400 uppercase tracking-wide">Formation</p>
            <p className="font-medium text-zinc-900 dark:text-zinc-100">{gameFormation}</p>
          </div>
          <div className="space-y-1">
            <p className="text-xs text-zinc-500 dark:text-zinc-400 uppercase tracking-wide">Dauer</p>
            <p className="font-medium text-zinc-900 dark:text-zinc-100">
              {currentSession.duration_minutes || 90} Min
            </p>
          </div>
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-team-primary/5 rounded-full -translate-y-16 translate-x-16" />
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-team-primary/5 rounded-full translate-y-12 -translate-x-12" />
    </div>
  );
}

// Game Planning Section Component
function GamePlanningSection({
  gameDate,
  gameTime,
  currentSession,
  onDateChange,
  onTimeChange,
  onSave,
}: {
  gameDate: Date;
  gameTime: string;
  currentSession: GameSession | null;
  onDateChange: (date: Date | undefined) => void;
  onTimeChange: (time: string) => void;
  onSave: () => void;
}) {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 sm:grid-cols-2">
        <div className="space-y-3">
          <label className="text-sm font-semibold text-zinc-900 dark:text-zinc-100 flex items-center gap-2">
            <CalendarIcon className="w-4 h-4" />
            Spieldatum
          </label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal h-12 bg-white dark:bg-zinc-800/50 border-zinc-200 dark:border-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-800",
                  !gameDate && "text-muted-foreground dark:text-zinc-400"
                )}
              >
                <CalendarIcon className="mr-3 h-5 w-5 text-zinc-500" />
                {gameDate ? (
                  <span className="font-medium">
                    {format(gameDate, "dd.MM.yyyy", { locale: de })}
                  </span>
                ) : (
                  <span>Datum wählen</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="w-auto p-0 dark:bg-zinc-800 dark:border-zinc-700"
              align="start"
            >
              <CalendarComponent
                mode="single"
                selected={gameDate}
                onSelect={onDateChange}
                disabled={(date) => date < new Date()}
                initialFocus
                locale={de}
                className="dark:bg-zinc-800"
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-3">
          <label className="text-sm font-semibold text-zinc-900 dark:text-zinc-100 flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Spielzeit
          </label>
          <div className="relative">
            <Clock className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-zinc-500 dark:text-zinc-400" />
            <Input
              type="time"
              value={gameTime}
              onChange={(e) => onTimeChange(e.target.value)}
              className="pl-12 h-12 bg-white dark:bg-zinc-800/50 border-zinc-200 dark:border-zinc-700 text-base font-medium"
            />
          </div>
        </div>
      </div>

      <Button
        onClick={onSave}
        className="w-full bg-team-primary text-white hover:bg-team-primary/90 dark:bg-team-primary dark:hover:bg-team-primary/90 h-12 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
      >
        {currentSession?.status === "scheduled" ? (
          <>
            <Settings className="mr-3 h-5 w-5" />
            Spielplan aktualisieren
          </>
        ) : (
          <>
            <CalendarDays className="mr-3 h-5 w-5" />
            Neues Spiel erstellen
          </>
        )}
      </Button>
    </div>
  );
}

// Game Actions Section Component
function GameActionsSection({
  currentSession,
  canGenerateTeams,
  showMarkAsPlayedButton,
  isGeneratingTeams,
  isMarkingAsPlayed,
  isCancelingMatch,
  isPendingTeamApproval = false,
  isSignupOpen,
  onToggleSignup,
  onGenerateTeams,
  onMarkAsPlayed,
  onCancelMatch,
}: Omit<
  GamePlanningCardProps,
  "gameDate" | "gameTime" | "onDateChange" | "onTimeChange" | "onSave" | "gameFormation"
>) {
  if (!currentSession?.status) {
    return (
      <div className="text-center py-8 text-zinc-500 dark:text-zinc-400">
        <Activity className="w-12 h-12 mx-auto mb-3 opacity-50" />
        <p>Erstellen Sie zuerst ein Spiel, um Aktionen zu verwalten</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Team Generation Status */}
      {currentSession.isTeamGenerated && (
        <div className="flex items-center gap-3 rounded-xl border border-green-200 bg-green-50 p-4 dark:bg-green-950/30 dark:border-green-800">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-500/10 dark:bg-green-500/20">
            <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
          </div>
          <div className="flex-1">
            <p className="font-semibold text-green-900 dark:text-green-400">Teams generiert</p>
            <p className="text-sm text-green-700 dark:text-green-500">
              Die Teams wurden erfolgreich erstellt
            </p>
            {isPendingTeamApproval && (
              <Badge
                variant="outline"
                className="mt-2 border-amber-200 bg-amber-50 text-amber-700 dark:bg-amber-950/30 dark:border-amber-800 dark:text-amber-400"
              >
                Noch nicht gespeichert
              </Badge>
            )}
          </div>
        </div>
      )}

      {/* Primary Actions */}
      <div className="grid gap-3">
        <ActionButton
          label={isSignupOpen ? "Anmeldung schließen" : "Anmeldung öffnen"}
          icon={isSignupOpen ? Pause : Play}
          onClick={onToggleSignup}
          variant={isSignupOpen ? "warning" : "success"}
          confirmText={
            isSignupOpen
              ? "Diese Aktion schließt die Anmeldung für das aktuelle Spiel. Spieler können sich danach nicht mehr anmelden oder ihren Status ändern."
              : "Diese Aktion öffnet die Anmeldung für das aktuelle Spiel. Spieler können sich dann anmelden und ihren Status ändern."
          }
          confirmButtonText={isSignupOpen ? "Ja, Anmeldung schließen" : "Ja, Anmeldung öffnen"}
        />

        {canGenerateTeams && (
          <ActionButton
            label="Teams generieren"
            icon={Zap}
            onClick={onGenerateTeams}
            variant="primary"
            isLoading={isGeneratingTeams}
            loadingText="Teams werden generiert..."
            confirmText="Diese Aktion generiert automatisch Teams basierend auf den Spielerbewertungen. Die Anmeldung wird automatisch geschlossen und die Teams werden erstellt."
            confirmButtonText="Ja, Teams generieren"
          />
        )}

        {showMarkAsPlayedButton && (
          <ActionButton
            label="Als gespielt markieren"
            icon={Target}
            onClick={onMarkAsPlayed}
            variant="success"
            isLoading={isMarkingAsPlayed}
            loadingText="Wird markiert..."
            confirmText="Diese Aktion markiert das Spiel als gespielt und archiviert es. Das Spiel wird dann in der Historie verfügbar sein."
            confirmButtonText="Ja, als gespielt markieren"
          />
        )}

        <ActionButton
          label="Spiel abbrechen"
          icon={RotateCcw}
          onClick={onCancelMatch}
          variant="danger"
          isLoading={isCancelingMatch}
          loadingText="Wird abgebrochen..."
          confirmText="Diese Aktion bricht das aktuelle Spiel ab und archiviert es als abgebrochen. Dies kann nicht rückgängig gemacht werden."
          confirmButtonText="Ja, Spiel abbrechen"
        />
      </div>
    </div>
  );
}

interface ActionButtonProps {
  label: string;
  icon: React.ElementType;
  onClick: () => void;
  variant: "primary" | "success" | "warning" | "danger";
  isLoading?: boolean;
  loadingText?: string;
  confirmText: string;
  confirmButtonText: string;
}

function ActionButton({
  label,
  icon: Icon,
  onClick,
  variant,
  isLoading,
  loadingText,
  confirmText,
  confirmButtonText,
}: ActionButtonProps) {
  const variants = {
    primary:
      "bg-gradient-to-r from-team-primary/10 to-team-primary/5 text-team-primary hover:from-team-primary/20 hover:to-team-primary/10 border border-team-primary/20 dark:from-team-primary/20 dark:to-team-primary/10 dark:text-team-primary dark:hover:from-team-primary/30 dark:hover:to-team-primary/20",
    success:
      "bg-gradient-to-r from-green-500/10 to-green-500/5 text-green-600 hover:from-green-500/20 hover:to-green-500/10 border border-green-500/20 dark:from-green-500/20 dark:to-green-500/10 dark:text-green-400 dark:hover:from-green-500/30 dark:hover:to-green-500/20",
    warning:
      "bg-gradient-to-r from-amber-500/10 to-amber-500/5 text-amber-600 hover:from-amber-500/20 hover:to-amber-500/10 border border-amber-500/20 dark:from-amber-500/20 dark:to-amber-500/10 dark:text-amber-400 dark:hover:from-amber-500/30 dark:hover:to-amber-500/20",
    danger:
      "bg-gradient-to-r from-red-500/10 to-red-500/5 text-red-600 hover:from-red-500/20 hover:to-red-500/10 border border-red-500/20 dark:from-red-500/20 dark:to-red-500/10 dark:text-red-400 dark:hover:from-red-500/30 dark:hover:to-red-500/20",
  };

  const confirmVariants = {
    primary:
      "bg-team-primary hover:bg-team-primary/90 dark:bg-team-primary dark:hover:bg-team-primary/90",
    success: "bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600",
    warning: "bg-amber-600 hover:bg-amber-700 dark:bg-amber-500 dark:hover:bg-amber-600",
    danger: "bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600",
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-start h-14 text-base font-medium rounded-xl transition-all duration-200 hover:shadow-md",
            variants[variant]
          )}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" className="mr-3" />
              <span>{loadingText}</span>
            </>
          ) : (
            <>
              <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-current/10 mr-3">
                <Icon className="h-4 w-4" />
              </div>
              <span className="flex-1 text-left">{label}</span>
              <ChevronRight className="h-5 w-5 opacity-50" />
            </>
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent className="dark:bg-zinc-900 dark:border-zinc-800">
        <AlertDialogHeader>
          <AlertDialogTitle className="dark:text-white flex items-center gap-2">
            <Icon className="h-5 w-5" />
            {label}
          </AlertDialogTitle>
          <AlertDialogDescription className="dark:text-zinc-400">
            {confirmText}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel className="dark:bg-zinc-800 dark:hover:bg-zinc-700 dark:text-zinc-300 dark:border-zinc-700">
            Abbrechen
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onClick}
            className={cn("text-white", confirmVariants[variant])}
          >
            {confirmButtonText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export function GamePlanningCard(props: GamePlanningCardProps) {
  return (
    <Card className="dark:bg-zinc-900 dark:border-zinc-800 overflow-hidden">
      <CardHeader className="pb-4">
        <div className="space-y-1">
          <CardTitle className="dark:text-white flex items-center gap-3 text-xl">
            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-team-primary/10 dark:bg-team-primary/20">
              <Settings className="w-5 h-5 text-team-primary" />
            </div>
            Spiel Control Center
          </CardTitle>
          <CardDescription className="dark:text-zinc-400 text-base">
            Zentrale Steuerung für Spielplanung, Teams und Aktionen
          </CardDescription>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Game Status Header */}
        <GameStatusHeader
          currentSession={props.currentSession}
          gameFormation={props.gameFormation}
          isSignupOpen={props.isSignupOpen}
        />

        {/* Tabs for different sections */}
        <Tabs defaultValue="planning" className="w-full">
          <TabsList className="grid w-full grid-cols-2 h-12 bg-zinc-100 dark:bg-zinc-800 rounded-xl p-1">
            <TabsTrigger
              value="planning"
              className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm dark:data-[state=active]:bg-zinc-700"
            >
              <CalendarDays className="w-4 h-4 mr-2" />
              Planung
            </TabsTrigger>
            <TabsTrigger
              value="actions"
              className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm dark:data-[state=active]:bg-zinc-700"
            >
              <Activity className="w-4 h-4 mr-2" />
              Aktionen
            </TabsTrigger>
          </TabsList>

          <TabsContent value="planning" className="mt-6 space-y-0">
            <GamePlanningSection
              gameDate={props.gameDate}
              gameTime={props.gameTime}
              currentSession={props.currentSession}
              onDateChange={props.onDateChange}
              onTimeChange={props.onTimeChange}
              onSave={props.onSave}
            />
          </TabsContent>

          <TabsContent value="actions" className="mt-6 space-y-0">
            <GameActionsSection
              currentSession={props.currentSession}
              canGenerateTeams={props.canGenerateTeams}
              showMarkAsPlayedButton={props.showMarkAsPlayedButton}
              isGeneratingTeams={props.isGeneratingTeams}
              isMarkingAsPlayed={props.isMarkingAsPlayed}
              isCancelingMatch={props.isCancelingMatch}
              isPendingTeamApproval={props.isPendingTeamApproval}
              isSignupOpen={props.isSignupOpen}
              onToggleSignup={props.onToggleSignup}
              onGenerateTeams={props.onGenerateTeams}
              onMarkAsPlayed={props.onMarkAsPlayed}
              onCancelMatch={props.onCancelMatch}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
